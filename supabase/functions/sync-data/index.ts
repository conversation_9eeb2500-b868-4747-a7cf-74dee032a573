
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const { action, user_id } = await req.json();

    if (!user_id) {
      return new Response(
        JSON.stringify({ error: "User ID is required" }),
        { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    if (action === 'fetch_appointments') {
      // Here you could fetch appointments from an external API 
      // For now, we'll just return the user's appointments from the database
      const { data, error } = await supabaseClient
        .from('appointments')
        .select('*')
        .eq('user_id', user_id);

      if (error) throw error;

      return new Response(
        JSON.stringify(data),
        { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    if (action === 'update_energy') {
      // Simulate retrieving energy data from an external service
      // This could be replaced with an actual API call to an energy provider
      
      // Get current data
      const { data: currentData, error: fetchError } = await supabaseClient
        .from('energy_data')
        .select('*')
        .eq('user_id', user_id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') throw fetchError;

      // Simulate some updated values
      const updatedUsage = currentData ? Math.round(currentData.current_usage * (1 + (Math.random() * 0.1 - 0.05))) : 320;
      const daysLeft = currentData ? Math.max(1, currentData.days_left - 1) : 12;
      const projectedBill = currentData ? Math.round(updatedUsage * (currentData.cost_per_kwh || 0.52)) : 234;

      // Update the database
      const { data, error } = await supabaseClient
        .from('energy_data')
        .update({
          current_usage: updatedUsage,
          days_left: daysLeft,
          projected_bill: projectedBill,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user_id)
        .select()
        .single();

      if (error) throw error;

      return new Response(
        JSON.stringify(data),
        { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    return new Response(
      JSON.stringify({ error: "Invalid action" }),
      { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );

  } catch (error) {
    console.error("Error processing request:", error);
    
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});
