
import React from 'react';
import { useDashboard } from '@/context/DashboardContext';
import { Car, Calendar, Droplet, Gauge } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format, differenceInDays } from 'date-fns';
import { pt } from 'date-fns/locale';

const CarCard = () => {
  const { data } = useDashboard();
  const { model, nextMaintenance, lastGasFill, gasMileage } = data.car;

  // Calculate days until next maintenance
  const daysUntilMaintenance = differenceInDays(nextMaintenance, new Date());

  return (
    <Card className="card-game">
      <CardHeader className="card-header flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-2xl">Automóvel</CardTitle>
        <Car className="h-6 w-6 text-farm-brown" />
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex items-center justify-center bg-gray-50 p-3 rounded-lg border border-gray-100 mb-4">
          <Car className="h-5 w-5 text-farm-blue mr-2" />
          <span className="text-xl font-bubblegum text-farm-blue">{model}</span>
        </div>

        <div className="grid grid-cols-1 gap-3">
          <div className="flex items-center bg-white rounded-lg border border-gray-100 p-3">
            <Calendar className="h-5 w-5 text-farm-red mr-3" />
            <div>
              <p className="text-sm text-gray-500">Próxima Manutenção</p>
              <p className="font-semibold">
                {format(nextMaintenance, "dd 'de' MMMM", { locale: pt })}
                <span className="ml-2 text-sm text-farm-red">
                  (em {daysUntilMaintenance} dias)
                </span>
              </p>
            </div>
          </div>

          <div className="flex items-center bg-white rounded-lg border border-gray-100 p-3">
            <Droplet className="h-5 w-5 text-farm-blue mr-3" />
            <div>
              <p className="text-sm text-gray-500">Último Abastecimento</p>
              <p className="font-semibold">
                {format(lastGasFill, "dd 'de' MMMM", { locale: pt })}
              </p>
            </div>
          </div>

          <div className="flex items-center bg-white rounded-lg border border-gray-100 p-3">
            <Gauge className="h-5 w-5 text-farm-green mr-3" />
            <div>
              <p className="text-sm text-gray-500">Consumo Médio</p>
              <p className="font-semibold">
                {gasMileage} km/l
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CarCard;
