
import React from 'react';
import { useDashboard } from '@/context/DashboardContext';
import { Target } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import GoalIcon from '@/components/icons/GoalIcons';

const GoalCard = () => {
  const { data } = useDashboard();
  const { goals } = data;

  return (
    <Card className="card-game">
      <CardHeader className="card-header flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-2xl">Sonhos & Metas</CardTitle>
        <Target className="h-6 w-6 text-farm-brown" />
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-4">
          {goals.map((goal) => {
            const percentage = Math.round((goal.current / goal.target) * 100);
            
            return (
              <div key={goal.id} className="bg-white rounded-lg border border-gray-100 p-3 hover:shadow-md transition-shadow">
                <div className="flex items-center gap-3 mb-2">
                  <div className={`${goal.color} w-10 h-10 rounded-lg flex items-center justify-center`}>
                    <GoalIcon type={goal.icon as any} size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{goal.name}</h3>
                    <div className="flex items-center text-sm text-gray-600">
                      <span>{goal.current.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}</span>
                      <span className="mx-1">/</span>
                      <span>{goal.target.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}</span>
                    </div>
                  </div>
                </div>
                
                <div className="progress-container">
                  <div 
                    className={`progress-bar ${goal.color}`} 
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <div className="flex justify-end mt-1">
                  <span className="text-xs font-bold">{percentage}%</span>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default GoalCard;
