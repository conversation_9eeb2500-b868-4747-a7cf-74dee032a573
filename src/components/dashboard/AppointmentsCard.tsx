
import React from 'react';
import { useDashboard } from '@/context/DashboardContext';
import { Calendar, User, Briefcase, Stethoscope, Car } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';

const AppointmentsCard = () => {
  const { data } = useDashboard();
  const { appointments } = data;

  // Sort appointments by date
  const sortedAppointments = [...appointments].sort((a, b) => a.date.getTime() - b.date.getTime());

  // Get icon based on appointment type
  const getAppointmentIcon = (type: string) => {
    switch (type) {
      case 'personal':
        return <User className="h-4 w-4 text-purple-600" />;
      case 'work':
        return <Briefcase className="h-4 w-4 text-blue-600" />;
      case 'health':
        return <Stethoscope className="h-4 w-4 text-red-600" />;
      case 'car':
        return <Car className="h-4 w-4 text-green-600" />;
      default:
        return <Calendar className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <Card className="card-game">
      <CardHeader className="card-header flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-2xl">Compromissos</CardTitle>
        <Calendar className="h-6 w-6 text-farm-brown" />
      </CardHeader>
      <CardContent className="p-4">
        {sortedAppointments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>Nenhum compromisso agendado</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedAppointments.map((appointment) => (
              <div 
                key={appointment.id} 
                className="flex items-center p-3 bg-white rounded-lg border border-gray-100 hover:shadow-md transition-shadow"
              >
                <div className="mr-3 bg-gray-100 p-2 rounded-full">
                  {getAppointmentIcon(appointment.type)}
                </div>
                <div className="flex-grow">
                  <h3 className="font-semibold">{appointment.title}</h3>
                  <p className="text-sm text-gray-600">
                    {format(appointment.date, "PPP 'às' HH'h'mm", { locale: pt })}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AppointmentsCard;
