
import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { LogOut, User, RefreshCw } from 'lucide-react';
import { updateEnergyData } from '@/services/api';
import { useDashboard } from '@/context/DashboardContext';
import { useToast } from '@/hooks/use-toast';

const Header = () => {
  const { user, signOut } = useAuth();
  const { data, loading } = useDashboard();
  const { toast } = useToast();
  
  const firstName = user?.user_metadata?.first_name || 'Usuário';
  
  const handleSyncData = async () => {
    if (!user) return;
    
    try {
      toast({
        title: "Sincronizando dados...",
        description: "Aguarde enquanto atualizamos seus dados de energia.",
      });
      
      await updateEnergyData(user.id);
      
      toast({
        title: "Dados sincronizados",
        description: "Seus dados de energia foram atualizados com sucesso.",
      });
      
      // Refresh the page to see updated data
      window.location.reload();
    } catch (error) {
      console.error('Error syncing data:', error);
      toast({
        variant: "destructive",
        title: "Erro ao sincronizar dados",
        description: "Ocorreu um erro ao atualizar seus dados. Tente novamente mais tarde.",
      });
    }
  };
  
  return (
    <header className="flex justify-between items-center py-4 mb-6">
      <div>
        <h1 className="text-3xl font-bold text-farm-brown">Happy Life</h1>
        <p className="text-gray-600">Seja bem-vindo, {firstName}!</p>
      </div>
      
      <div className="flex items-center gap-3">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleSyncData}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
        <Button variant="outline" size="sm" onClick={() => signOut()}>
          <LogOut className="h-4 w-4 mr-2" />
          Sair
        </Button>
      </div>
    </header>
  );
};

export default Header;
