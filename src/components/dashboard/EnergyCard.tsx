
import React from 'react';
import { useDashboard } from '@/context/DashboardContext';
import { Zap, AlertTriangle, Calendar, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

const EnergyCard = () => {
  const { data } = useDashboard();
  const { currentUsage, monthAverage, costPerKwh, projectedBill, daysLeft } = data.energy;
  
  // Calculate percentage of current usage compared to average
  const usagePercentage = (currentUsage / monthAverage) * 100;
  
  // Determine color based on usage
  let progressColor = "bg-farm-green";
  if (usagePercentage > 90) {
    progressColor = "bg-farm-red";
  } else if (usagePercentage > 75) {
    progressColor = "bg-farm-yellow";
  }

  return (
    <Card className="card-game">
      <CardHeader className="card-header flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-2xl">Energia</CardTitle>
        <Zap className="h-6 w-6 text-farm-brown" />
      </CardHeader>
      <CardContent className="p-4">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-semibold">Consumo Atual</span>
            <span className="text-sm">{currentUsage} kWh</span>
          </div>
          <div className="progress-container">
            <div 
              className={`progress-bar ${progressColor}`} 
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-end mt-1">
            <span className="text-xs text-gray-500">
              {monthAverage} kWh média
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <div className="flex items-center">
              <TrendingUp className="h-4 w-4 text-farm-blue mr-1" />
              <span className="text-sm font-semibold">Custo por kWh</span>
            </div>
            <p className="text-lg font-bold mt-1 text-farm-blue">
              {costPerKwh.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
            </p>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg border border-blue-100">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 text-farm-blue mr-1" />
              <span className="text-sm font-semibold">Próxima conta</span>
            </div>
            <p className="text-lg font-bold mt-1 text-farm-blue">
              {projectedBill.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
            </p>
          </div>
        </div>

        <div className="flex items-center justify-center bg-amber-50 p-2 rounded-lg border border-amber-100">
          <AlertTriangle className="h-4 w-4 text-amber-600 mr-2" />
          <span className="text-sm text-amber-700">
            Faltam {daysLeft} dias para o fechamento
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnergyCard;
