
import React from 'react';
import { useDashboard } from '@/context/DashboardContext';
import { ArrowUpRight, ArrowDownRight, Coins } from 'lucide-react';
import CoinIcon from '@/components/icons/CoinIcon';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const FinancialCard = () => {
  const { data } = useDashboard();
  const { totalBalance, lastIncome, lastExpense } = data.financial;

  return (
    <Card className="card-game">
      <CardHeader className="card-header flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-2xl">Finanças</CardTitle>
        <Coins className="h-6 w-6 text-farm-brown" />
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex items-center justify-center mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center">
              <CoinIcon size={32} className="mr-2 animate-float" />
              <span className="text-3xl font-bubblegum text-farm-brown">
                {totalBalance.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
              </span>
            </div>
            <CardDescription>Saldo Total</CardDescription>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="bg-green-50 p-3 rounded-lg border border-green-100">
            <div className="flex items-center">
              <ArrowUpRight className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-xl font-semibold text-green-600">
                {lastIncome.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
              </span>
            </div>
            <p className="text-sm text-green-700 mt-1">Última Entrada</p>
          </div>

          <div className="bg-red-50 p-3 rounded-lg border border-red-100">
            <div className="flex items-center">
              <ArrowDownRight className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-xl font-semibold text-red-600">
                {lastExpense.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
              </span>
            </div>
            <p className="text-sm text-red-700 mt-1">Última Saída</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FinancialCard;
