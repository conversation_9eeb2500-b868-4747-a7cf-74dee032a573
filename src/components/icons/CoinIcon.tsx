
import React from 'react';

const CoinIcon = ({ size = 24, className = "" }: { size?: number, className?: string }) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      className={className}
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="12" cy="12" r="10" fill="#FFD75E" stroke="#9B6A4B" strokeWidth="1.5" />
      <circle cx="12" cy="12" r="7" fill="#FFEB99" />
      <path d="M12 7V17" stroke="#9B6A4B" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M9 10L12 7L15 10" stroke="#9B6A4B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default CoinIcon;
