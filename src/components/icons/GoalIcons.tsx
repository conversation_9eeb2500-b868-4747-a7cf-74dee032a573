
import React from 'react';
import { 
  Car, 
  Home, 
  Palmtree, 
  GraduationCap, 
  Baby, 
  Computer
} from 'lucide-react';

type IconType = "car" | "home" | "palmtree" | "graduation" | "baby" | "computer";

interface GoalIconProps {
  type: IconType;
  size?: number;
  className?: string;
}

const GoalIcon: React.FC<GoalIconProps> = ({ type, size = 24, className = "" }) => {
  const iconProps = {
    size,
    className
  };

  switch (type) {
    case "car":
      return <Car {...iconProps} />;
    case "home":
      return <Home {...iconProps} />;
    case "palmtree":
      return <Palmtree {...iconProps} />;
    case "graduation":
      return <GraduationCap {...iconProps} />;
    case "baby":
      return <Baby {...iconProps} />;
    case "computer":
      return <Computer {...iconProps} />;
    default:
      return <Car {...iconProps} />;
  }
};

export default GoalIcon;
