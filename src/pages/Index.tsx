
import React from 'react';
import { DashboardProvider } from '@/context/DashboardContext';
import Header from '@/components/dashboard/Header';
import FinancialCard from '@/components/dashboard/FinancialCard';
import EnergyCard from '@/components/dashboard/EnergyCard';
import GoalCard from '@/components/dashboard/GoalCard';
import AppointmentsCard from '@/components/dashboard/AppointmentsCard';
import CarCard from '@/components/dashboard/CarCard';

const Index = () => {
  return (
    <DashboardProvider>
      <div className="min-h-screen max-w-full overflow-x-hidden p-4 md:p-6">
        <Header />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <FinancialCard />
          <EnergyCard />
          <CarCard />
          <div className="md:col-span-2">
            <GoalCard />
          </div>
          <div className="lg:col-span-3 md:col-span-2">
            <AppointmentsCard />
          </div>
        </div>
      </div>
    </DashboardProvider>
  );
};

export default Index;
