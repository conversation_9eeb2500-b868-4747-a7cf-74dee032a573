// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fmgkjjepkwxhyzlspljb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtZ2tqamVwa3d4aHl6bHNwbGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQzMDQ0MTcsImV4cCI6MjA1OTg4MDQxN30.WGde1iMVkJXKr7r78UDrKjp8IMzHV9Hx-2T-AElGt1k";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);