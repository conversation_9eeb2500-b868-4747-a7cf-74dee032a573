import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';

// Define our dashboard data types
interface FinancialData {
  totalBalance: number;
  lastIncome: number;
  lastExpense: number;
}

interface EnergyData {
  currentUsage: number;
  monthAverage: number;
  costPerKwh: number;
  projectedBill: number;
  daysLeft: number;
}

interface Goal {
  id: number;
  name: string;
  icon: string;
  target: number;
  current: number;
  color: string;
}

interface AppointmentItem {
  id: number;
  title: string;
  date: Date;
  type: 'personal' | 'work' | 'health' | 'car';
}

interface CarData {
  model: string;
  nextMaintenance: Date;
  lastGasFill: Date;
  gasMileage: number;
}

interface DashboardData {
  financial: FinancialData;
  energy: EnergyData;
  goals: Goal[];
  appointments: AppointmentItem[];
  car: CarData;
}

// Update Supabase types to match actual database types
interface SupabaseFinancialData {
  id: string;
  user_id: string;
  total_balance: number;
  last_income: number;
  last_expense: number;
  created_at?: string;
  updated_at?: string;
}

interface SupabaseEnergyData {
  id: string;
  user_id: string;
  current_usage: number;
  month_average: number;
  cost_per_kwh: number;
  projected_bill: number;
  days_left: number;
  created_at?: string;
  updated_at?: string;
}

interface SupabaseGoal {
  id: string;
  user_id: string;
  name: string;
  icon: string;
  target: number;
  current: number;
  color: string;
  created_at?: string;
  updated_at?: string;
}

interface SupabaseAppointment {
  id: string;
  user_id: string;
  title: string;
  date: string;
  type: 'personal' | 'work' | 'health' | 'car';
  created_at?: string;
  updated_at?: string;
}

interface SupabaseCarData {
  id: string;
  user_id: string;
  model: string;
  next_maintenance: string;
  last_gas_fill: string;
  gas_mileage: number;
  created_at?: string;
  updated_at?: string;
}

// Create default data
const defaultData: DashboardData = {
  financial: {
    totalBalance: 15750.42,
    lastIncome: 3200,
    lastExpense: 1450.30
  },
  energy: {
    currentUsage: 320,
    monthAverage: 450,
    costPerKwh: 0.52,
    projectedBill: 234,
    daysLeft: 12
  },
  goals: [
    {
      id: 1,
      name: "Carro Novo",
      icon: "car",
      target: 45000,
      current: 12500,
      color: "bg-farm-blue"
    },
    {
      id: 2,
      name: "Férias",
      icon: "palmtree",
      target: 6000,
      current: 4200,
      color: "bg-farm-yellow"
    },
    {
      id: 3,
      name: "Reforma da Casa",
      icon: "home",
      target: 20000,
      current: 5600,
      color: "bg-farm-green"
    }
  ],
  appointments: [
    {
      id: 1,
      title: "Consulta médica",
      date: new Date(2025, 3, 20, 14, 30),
      type: "health"
    },
    {
      id: 2,
      title: "Revisão do carro",
      date: new Date(2025, 3, 25, 10, 0),
      type: "car"
    },
    {
      id: 3,
      title: "Reunião com cliente",
      date: new Date(2025, 3, 15, 9, 0),
      type: "work"
    }
  ],
  car: {
    model: "Toyota Corolla 2022",
    nextMaintenance: new Date(2025, 5, 10),
    lastGasFill: new Date(2025, 3, 2),
    gasMileage: 14.2
  }
};

// Create context
interface DashboardContextType {
  data: DashboardData;
  loading: boolean;
  error: string | null;
  updateFinancial: (financial: Partial<FinancialData>) => Promise<void>;
  updateEnergy: (energy: Partial<EnergyData>) => Promise<void>;
  updateGoals: (goalId: number, amount: number) => Promise<void>;
  addAppointment: (appointment: Omit<AppointmentItem, 'id'>) => Promise<void>;
  removeAppointment: (id: number) => Promise<void>;
  updateCar: (car: Partial<CarData>) => Promise<void>;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export const DashboardProvider = ({ children }: { children: ReactNode }) => {
  const [data, setData] = useState<DashboardData>(defaultData);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  // Fetch user's data from Supabase
  useEffect(() => {
    if (!user) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Initialize data with defaults if nothing is in the database yet
        await initUserData();

        // Fetch financial data
        const { data: financialData, error: financialError } = await supabase
          .from('financial_data')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (financialError && financialError.code !== 'PGRST116') {
          throw new Error(`Error fetching financial data: ${financialError.message}`);
        }

        // Fetch energy data
        const { data: energyData, error: energyError } = await supabase
          .from('energy_data')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (energyError && energyError.code !== 'PGRST116') {
          throw new Error(`Error fetching energy data: ${energyError.message}`);
        }

        // Fetch goals
        const { data: goalsData, error: goalsError } = await supabase
          .from('goals')
          .select('*')
          .eq('user_id', user.id);

        if (goalsError) {
          throw new Error(`Error fetching goals: ${goalsError.message}`);
        }

        // Fetch appointments
        const { data: appointmentsData, error: appointmentsError } = await supabase
          .from('appointments')
          .select('*')
          .eq('user_id', user.id);

        if (appointmentsError) {
          throw new Error(`Error fetching appointments: ${appointmentsError.message}`);
        }

        // Fetch car data
        const { data: carData, error: carError } = await supabase
          .from('car_data')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (carError && carError.code !== 'PGRST116') {
          throw new Error(`Error fetching car data: ${carError.message}`);
        }

        // Transform data to match our context structure
        const transformedData: DashboardData = {
          financial: financialData ? {
            totalBalance: Number(financialData.total_balance),
            lastIncome: financialData.last_income ? Number(financialData.last_income) : 0,
            lastExpense: financialData.last_expense ? Number(financialData.last_expense) : 0
          } : defaultData.financial,
          energy: energyData ? {
            currentUsage: Number(energyData.current_usage),
            monthAverage: energyData.month_average ? Number(energyData.month_average) : 0,
            costPerKwh: energyData.cost_per_kwh ? Number(energyData.cost_per_kwh) : 0,
            projectedBill: energyData.projected_bill ? Number(energyData.projected_bill) : 0,
            daysLeft: energyData.days_left || 0
          } : defaultData.energy,
          goals: goalsData ? goalsData.map(goal => ({
            id: parseInt(goal.id),
            name: goal.name,
            icon: goal.icon,
            target: Number(goal.target),
            current: Number(goal.current),
            color: goal.color
          })) : defaultData.goals,
          appointments: appointmentsData ? appointmentsData.map(appointment => ({
            id: parseInt(appointment.id),
            title: appointment.title,
            date: new Date(appointment.date),
            type: appointment.type as 'personal' | 'work' | 'health' | 'car'
          })) : defaultData.appointments,
          car: carData ? {
            model: carData.model,
            nextMaintenance: carData.next_maintenance ? new Date(carData.next_maintenance) : new Date(),
            lastGasFill: carData.last_gas_fill ? new Date(carData.last_gas_fill) : new Date(),
            gasMileage: carData.gas_mileage ? Number(carData.gas_mileage) : 0
          } : defaultData.car
        };

        setData(transformedData);
      } catch (err: any) {
        console.error("Error fetching dashboard data:", err);
        setError(err.message);
        toast({
          variant: "destructive",
          title: "Erro ao carregar dados",
          description: err.message,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, toast]);

  // Initialize user data if it doesn't exist in the database
  const initUserData = async () => {
    if (!user) return;

    try {
      // Check if financial data exists
      const { data: financialExists, error: financialCheckError } = await supabase
        .from('financial_data')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (financialCheckError && financialCheckError.code !== 'PGRST116') {
        throw financialCheckError;
      }

      // If no financial data, insert default
      if (!financialExists) {
        await supabase.from('financial_data').insert({
          user_id: user.id,
          total_balance: defaultData.financial.totalBalance,
          last_income: defaultData.financial.lastIncome,
          last_expense: defaultData.financial.lastExpense
        });
      }

      // Check if energy data exists
      const { data: energyExists, error: energyCheckError } = await supabase
        .from('energy_data')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (energyCheckError && energyCheckError.code !== 'PGRST116') {
        throw energyCheckError;
      }

      // If no energy data, insert default
      if (!energyExists) {
        await supabase.from('energy_data').insert({
          user_id: user.id,
          current_usage: defaultData.energy.currentUsage,
          month_average: defaultData.energy.monthAverage,
          cost_per_kwh: defaultData.energy.costPerKwh,
          projected_bill: defaultData.energy.projectedBill,
          days_left: defaultData.energy.daysLeft
        });
      }

      // Check if goals exist
      const { data: goalsExist, error: goalsCheckError } = await supabase
        .from('goals')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

      if (goalsCheckError) {
        throw goalsCheckError;
      }

      // If no goals, insert defaults
      if (!goalsExist || goalsExist.length === 0) {
        const goalsToInsert = defaultData.goals.map(goal => ({
          user_id: user.id,
          name: goal.name,
          icon: goal.icon,
          target: goal.target,
          current: goal.current,
          color: goal.color
        }));
        
        await supabase.from('goals').insert(goalsToInsert);
      }

      // Check if car data exists
      const { data: carExists, error: carCheckError } = await supabase
        .from('car_data')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (carCheckError && carCheckError.code !== 'PGRST116') {
        throw carCheckError;
      }

      // If no car data, insert default
      if (!carExists) {
        await supabase.from('car_data').insert({
          user_id: user.id,
          model: defaultData.car.model,
          next_maintenance: defaultData.car.nextMaintenance.toISOString(),
          last_gas_fill: defaultData.car.lastGasFill.toISOString(),
          gas_mileage: defaultData.car.gasMileage
        });
      }

      // Check if appointments exist
      const { data: appointmentsExist, error: appointmentsCheckError } = await supabase
        .from('appointments')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

      if (appointmentsCheckError) {
        throw appointmentsCheckError;
      }

      // If no appointments, insert defaults
      if (!appointmentsExist || appointmentsExist.length === 0) {
        const appointmentsToInsert = defaultData.appointments.map(appointment => ({
          user_id: user.id,
          title: appointment.title,
          date: appointment.date.toISOString(),
          type: appointment.type
        }));
        
        await supabase.from('appointments').insert(appointmentsToInsert);
      }
    } catch (err) {
      console.error("Error initializing user data:", err);
      throw err;
    }
  };

  const updateFinancial = async (financial: Partial<FinancialData>) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // Update local state
      const updatedFinancial = { ...data.financial, ...financial };
      setData(prev => ({ ...prev, financial: updatedFinancial }));
      
      // Update in Supabase
      const { error } = await supabase
        .from('financial_data')
        .update({
          total_balance: financial.totalBalance !== undefined ? financial.totalBalance : data.financial.totalBalance,
          last_income: financial.lastIncome !== undefined ? financial.lastIncome : data.financial.lastIncome,
          last_expense: financial.lastExpense !== undefined ? financial.lastExpense : data.financial.lastExpense,
        })
        .eq('user_id', user.id);
      
      if (error) throw error;
      
      toast({
        title: "Dados financeiros atualizados",
        description: "Seus dados financeiros foram atualizados com sucesso.",
      });
    } catch (err: any) {
      console.error("Error updating financial data:", err);
      setError(err.message);
      
      // Revert to previous state
      setData(prev => ({ ...prev }));
      
      toast({
        variant: "destructive",
        title: "Erro ao atualizar dados financeiros",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const updateEnergy = async (energy: Partial<EnergyData>) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // Update local state
      const updatedEnergy = { ...data.energy, ...energy };
      setData(prev => ({ ...prev, energy: updatedEnergy }));
      
      // Update in Supabase
      const { error } = await supabase
        .from('energy_data')
        .update({
          current_usage: energy.currentUsage !== undefined ? energy.currentUsage : data.energy.currentUsage,
          month_average: energy.monthAverage !== undefined ? energy.monthAverage : data.energy.monthAverage,
          cost_per_kwh: energy.costPerKwh !== undefined ? energy.costPerKwh : data.energy.costPerKwh,
          projected_bill: energy.projectedBill !== undefined ? energy.projectedBill : data.energy.projectedBill,
          days_left: energy.daysLeft !== undefined ? energy.daysLeft : data.energy.daysLeft,
        })
        .eq('user_id', user.id);
      
      if (error) throw error;
      
      toast({
        title: "Dados de energia atualizados",
        description: "Seus dados de energia foram atualizados com sucesso.",
      });
    } catch (err: any) {
      console.error("Error updating energy data:", err);
      setError(err.message);
      
      // Revert to previous state
      setData(prev => ({ ...prev }));
      
      toast({
        variant: "destructive",
        title: "Erro ao atualizar dados de energia",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const updateGoals = async (goalId: number, amount: number) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      const goalIndex = data.goals.findIndex(goal => goal.id === goalId);
      if (goalIndex === -1) throw new Error("Meta não encontrada");
      
      const goal = data.goals[goalIndex];
      const newAmount = Math.min(goal.current + amount, goal.target);
      
      const updatedGoals = [...data.goals];
      updatedGoals[goalIndex] = { ...goal, current: newAmount };
      setData(prev => ({ ...prev, goals: updatedGoals }));
      
      // Convert number to string for Supabase id
      const { error } = await supabase
        .from('goals')
        .update({
          current: newAmount
        })
        .eq('id', goalId.toString())
        .eq('user_id', user.id);
      
      if (error) throw error;
      
      toast({
        title: "Meta atualizada",
        description: "Sua meta foi atualizada com sucesso.",
      });
    } catch (err: any) {
      console.error("Error updating goal:", err);
      setError(err.message);
      setData(prev => ({ ...prev }));
      
      toast({
        variant: "destructive",
        title: "Erro ao atualizar meta",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const addAppointment = async (appointment: Omit<AppointmentItem, 'id'>) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      const { data: insertedData, error } = await supabase
        .from('appointments')
        .insert({
          user_id: user.id,
          title: appointment.title,
          date: appointment.date.toISOString(),
          type: appointment.type
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Validate and convert the response data
      if (!insertedData || typeof insertedData.id !== 'string') {
        throw new Error('Invalid response data from server');
      }

      const newAppointment: AppointmentItem = {
        id: Number(insertedData.id), // Convert string id to number safely
        title: insertedData.title,
        date: new Date(insertedData.date),
        type: insertedData.type as 'personal' | 'work' | 'health' | 'car'
      };
      
      setData(prev => ({
        ...prev,
        appointments: [...prev.appointments, newAppointment]
      }));
      
      toast({
        title: "Compromisso adicionado",
        description: "Seu compromisso foi adicionado com sucesso.",
      });
    } catch (err: any) {
      console.error("Error adding appointment:", err);
      setError(err.message);
      
      toast({
        variant: "destructive",
        title: "Erro ao adicionar compromisso",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const removeAppointment = async (id: number) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      setData(prev => ({
        ...prev,
        appointments: prev.appointments.filter(a => a.id !== id)
      }));
      
      // Convert number to string for Supabase id and validate
      const supabaseId = String(id);
      if (!supabaseId) {
        throw new Error('Invalid appointment ID');
      }

      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', supabaseId)
        .eq('user_id', user.id);
      
      if (error) throw error;
      
      toast({
        title: "Compromisso removido",
        description: "Seu compromisso foi removido com sucesso.",
      });
    } catch (err: any) {
      console.error("Error removing appointment:", err);
      setError(err.message);
      setData(prev => ({ ...prev }));
      
      toast({
        variant: "destructive",
        title: "Erro ao remover compromisso",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const updateCar = async (car: Partial<CarData>) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // Update local state
      const updatedCar = { ...data.car, ...car };
      setData(prev => ({ ...prev, car: updatedCar }));
      
      // Update in Supabase
      const { error } = await supabase
        .from('car_data')
        .update({
          model: car.model !== undefined ? car.model : data.car.model,
          next_maintenance: car.nextMaintenance !== undefined ? car.nextMaintenance.toISOString() : data.car.nextMaintenance.toISOString(),
          last_gas_fill: car.lastGasFill !== undefined ? car.lastGasFill.toISOString() : data.car.lastGasFill.toISOString(),
          gas_mileage: car.gasMileage !== undefined ? car.gasMileage : data.car.gasMileage,
        })
        .eq('user_id', user.id);
      
      if (error) throw error;
      
      toast({
        title: "Dados do automóvel atualizados",
        description: "Os dados do seu automóvel foram atualizados com sucesso.",
      });
    } catch (err: any) {
      console.error("Error updating car data:", err);
      setError(err.message);
      
      // Revert to previous state
      setData(prev => ({ ...prev }));
      
      toast({
        variant: "destructive",
        title: "Erro ao atualizar dados do automóvel",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardContext.Provider 
      value={{ 
        data, 
        loading,
        error,
        updateFinancial,
        updateEnergy,
        updateGoals,
        addAppointment,
        removeAppointment,
        updateCar
      }}
    >
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
