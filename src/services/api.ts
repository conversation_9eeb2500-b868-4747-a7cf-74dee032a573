
import { supabase } from '@/integrations/supabase/client';

export const syncAppointments = async (userId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('sync-data', {
      body: {
        action: 'fetch_appointments',
        user_id: userId
      }
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error syncing appointments:', error);
    throw error;
  }
};

export const updateEnergyData = async (userId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('sync-data', {
      body: {
        action: 'update_energy',
        user_id: userId
      }
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating energy data:', error);
    throw error;
  }
};
